__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012324
    default.custom: 1757159088
    easy_en.custom: 0
    easy_en.schema: 1757155313
    key_bindings: 1749012324
    key_bindings.custom: 0
    punctuation: 1749012324
    punctuation.custom: 0
easy_en:
  split_sentence: true
  use_wordninja_py: false
  use_wordninja_rs: false
  use_wordninja_rs_lua_module: false
  wordninja_rs_lua_module_path: "/usr/lib/lua/5.4/wordninja.so"
  wordninja_rs_path: "/usr/bin/wordninja"
engine:
  filters:
    - uniquifier
    - "lua_filter@*easy_en*enhance_filter"
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
key_binder:
  bindings:
    - {accept: bracketleft, send: Page_Up, when: paging}
    - {accept: bracketright, send: Page_Down, when: has_menu}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
  import_preset: default
  select_first_character: comma
  select_last_character: period
menu:
  page_size: 9
recognizer:
  import_preset: default
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: ""
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema:
  author:
    - "Patrick <<EMAIL>>"
    - "BlindingDark <<EMAIL>>"
  description: "Easy English"
  name: "Easy English"
  schema_id: easy_en
  version: 0.10.1
speller:
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  delimiter: " '"
switches:
  - name: ascii_mode
    reset: 0
    states: ["ASCII-OFF", "ASCII-ON"]
translator:
  comment_format:
    - "xform/^.+$//"
  dictionary: easy_en
  enable_user_dict: true
  spelling_hints: 9
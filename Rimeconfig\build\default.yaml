__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012324
    default.custom: 1757159088
    key_bindings: 1749012323
    key_bindings.custom: 0
    punctuation: 1749012324
    punctuation.custom: 0
ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Caps_Lock: clear
    Control_L: noop
    Control_R: noop
    Eisu_toggle: clear
    Shift_L: inline_ascii
    Shift_R: commit_text
config_version: 0.40
key_binder:
  bindings:
    - {accept: bracketleft, send: Page_Up, when: paging}
    - {accept: bracketright, send: Page_Down, when: has_menu}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
  select_first_character: comma
  select_last_character: period
menu:
  page_size: 9
punctuator:
  full_shape:
    " ": {commit: "　"}
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": ["＃", "⌘"]
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["％", "°", "℃"]
    "&": "＆"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["＊", "·", "・", "×", "※", "❂"]
    "+": "＋"
    ",": {commit: "，"}
    "-": "－"
    .: {commit: "。"}
    "/": ["／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "＝"
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": ["＠", "☯"]
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "｀"
    "{": ["『", "〖", "｛"]
    "|": ["·", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": "～"
  half_shape:
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": "#"
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["%", "％", "°", "℃"]
    "&": "&"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["*", "＊", "·", "・", "×", "※", "❂"]
    "+": "+"
    ",": {commit: "，"}
    "-": "-"
    .: {commit: "。"}
    "/": ["、", "/", "／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "="
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": "@"
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "\\", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "`"
    "{": ["『", "〖", "｛"]
    "|": ["·", "|", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": ["~", "～"]
recognizer:
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema_list:
  - schema: luna_pinyin_simp
switcher:
  abbreviate_options: true
  caption: "〔方案選單〕"
  fold_options: true
  hotkeys:
    - "Control+grave"
    - "Control+Shift+grave"
    - F4
  option_list_separator: "／"
  save_options:
    - full_shape
    - ascii_punct
    - simplification
    - extended_charset
    - zh_hant
    - zh_hans
    - zh_hant_tw
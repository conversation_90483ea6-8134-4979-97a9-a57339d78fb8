patch:
  #确保中英文混输配置不干扰开关
  _include: easy_en:/patch
  easy_en/enable_sentence: false

  #开关配置
  switches:
    - name: ascii_mode
      reset: 0
      states: [中文, 西文]
    - name: full_shape
      reset: 1
      states: [半角, 全角]
    - name: zh_simp
      reset: 1
      states: [漢字, 汉字]
    - name: ascii_punct
      reset: 0
      states: [中文标点, 英文标点]

  #修复后的开关绑定
  switch_bindings:
    - when: ascii_mode@1  # 英文模式
      set:
        - full_shape@0   # 强制半角
        - ascii_punct@1  # 强制英文标点
    - when: ascii_mode@0  # 中文模式
      set:
        - full_shape@1   # 强制全角
        - ascii_punct@0  # 强制中文标点

  #标点映射（保持不变）
  punctuator:
    import_preset: default
    half_shape:
      ",": ","
      ".": "."
      "?": "?"
      ";": ";"
      "'": "'"
      "[": "["
      "]": "]"
      "\\": "\\"
      "`": "`"
      "-": "-"
      "=": "="
      "!": "!"
      "@": "@"
      "#": "#"
      "$": "$"
      "%": "%"
      "^": "^"
      "&": "&"
      "*": "*"
      "(": "("
      ")": ")"
      "_": "_"
      "+": "+"
      "{": "{"
      "}": "}"
      "|": "|"
      ":": ":"
      "<": "<"
      ">": ">"
      "/": "/"
      "~": "~"

    full_shape:
      ",": "，"
      ".": "。"
      "?": "？"
      ";": "；"
      "'": "‘"
      "[": "「"
      "]": "」"
      "\\": "、"
      "`": "·"
      "-": "－"
      "=": "＝"
      "!": "！"
      "@": "＠"
      "#": "＃"
      "$": "￥"
      "%": "％"
      "^": "……"
      "&": "＆"
      "*": "＊"
      "(": "（"
      ")": "）"
      "_": "——"
      "+": "＋"
      "{": "｛"
      "}": "｝"
      "|": "｜"
      ":": "："
      "<": "《"
      ">": "》"
      "/": "／"
      "~": "～"

    symbols:
      _include: symbols_v:/symbols

  recognizer:
    patterns:
      punct: "^v([0-9]|10|[A-Za-z]+)$"

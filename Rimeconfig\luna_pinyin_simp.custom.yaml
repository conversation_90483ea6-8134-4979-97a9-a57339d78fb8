patch:
  #开关
  switches:
    - name: ascii_mode
      reset: 0
      states: [中文, 西文]
    - name: full_shape
      states: [半角, 全角]
    - name: zh_simp
      reset: 1
      states: [漢字, 汉字]
    - name: ascii_punct
      reset: 0
      states: [中文标点, 英文标点]
  #标点符号映射配置与emoji模式合并
  punctuator:
    import_preset: default
    half_shape:
      ",": "，"
      ".": "。"
      "<": "《"
      ">": "》"
      "/": "／"
      "?": "？"
      ";": "；"
      "'": "'"
      "[": "【"
      "]": "】"
      "\\": "、"
      "`": "·"
      "-": "-"
      "=": "="
      
      #符号键位
      "~": "～"
      "!": "！"
      "@": "@"
      "#": "#"
      "$": "￥"
      "%": "%"
      "^": "……"
      "&": "&"
      "*": "*"
      "(": "（"
      ")": "）"
      "_": "——"
      "+": "+"
      "{": "「"
      "}": "」"
      "|": "|"
      ":": "："
      "\"": "\""
    full_shape:
      ",": "，"
      ".": "。"
      "<": "《"
      ">": "》"
      "/": "／"
      "?": "？"
      ";": "；"
      "'": "'"
      "[": "【"
      "]": "】"
      "\\": "、"
      "`": "·"
      "-": "－"
      "=": "＝"
      
      #符号键位
      "~": "～"
      "!": "！"
      "@": "@"
      "#": "#"
      "$": "￥"
      "%": "%"
      "^": "……"
      "&": "&"
      "*": "*"
      "(": "（"
      ")": "）"
      "_": "_"
      "+": "+"
      "{": "「"
      "}": ""
      "|": "|"
      ":": "："
      "\"": "\""
    symbols:
      __include: symbols_v:/symbols
  recognizer:
    patterns:
      punct: "^v([0-9]|10|[A-Za-z]+)$"
  #中英文混输
  __include: easy_en:/patch
  easy_en/enable_sentence: false